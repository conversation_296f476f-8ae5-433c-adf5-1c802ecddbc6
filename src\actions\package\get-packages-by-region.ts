import { IApiResponse } from '@/types/response';
import { IPackage } from '@/types/package';

export const revalidate = 0;

export default async function getPackagesByRegionSlug(slug: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/region-slug/${slug}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}

export async function getPackagesByRegionId(regionId: string) {
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_URL}/package/region/${regionId}`,
    {
      cache: 'no-cache',
    }
  );
  const data: IApiResponse<IPackage[]> = await res.json();
  return data;
}
