'use client';

import { IOverview } from '@/types/home';
import { motion } from 'framer-motion';
import Overview from '../../fastpacking/components/overview';
import Image from 'next/image';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';

const features = [
    {
        title: "Explore Places You Couldn't Yourself",
        desc: 'All trips are led by certified expert guides, unlocking life experiences in places most never see.',
        icon: (
            <svg
                viewBox="0 0 48 48"
                className="h-16 w-16 stroke-current"
                fill="none"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <path d="M24 3 10 9v12c0 10 6 18 14 24 8-6 14-14 14-24V9L24 3Z" />
                <path d="m18 22 4 4 8-8" />
            </svg>
        ),
    },
    {
        title: 'Join a Small Like‑Minded Group',
        desc: '75% join our trips as solo travellers, with most in their 30s–50s. 95% give our group dynamic 5 stars.',
        icon: (
            <svg
                viewBox="0 0 48 48"
                className="h-16 w-16 stroke-current"
                fill="none"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <rect x="4" y="14" width="16" height="20" rx="3" />
                <rect x="28" y="14" width="16" height="20" rx="3" />
                <path d="M20 24h8" />
            </svg>
        ),
    },
    {
        title: 'Hassle‑Free From Start to Finish',
        desc: 'We’ve sorted the logistics, so you can just rock up and have a blast in the wild.',
        icon: (
            <svg
                viewBox="0 0 48 48"
                className="h-16 w-16 stroke-current"
                fill="none"
                strokeWidth={2}
                strokeLinecap="round"
                strokeLinejoin="round"
            >
                <circle cx="24" cy="12" r="5" />
                <path d="M18 22h12l4 20H14l4-20Z" />
                <path d="M14 40h20" />
            </svg>
        ),
    },
];

export default function OverviewSection({
    overview,
}: {
    overview: IOverview;
}) {
    return (
        <section className="container mx-auto px-4 py-8 ">
            <div className='grid grid-cols-1 md:grid-cols-2 gap-12'>
                <div className="order-2 md:order-1 grid grid-cols-2 gap-6 mt-0 md:mt-20">
                    {overview.points.map((f, i) => (
                        <div
                            key={i}
                            className="flex flex-col items-center text-center"
                        >
                            <div className="mb-5 text-blue-900">
                                <Image
                                    src={f.icon || '/placeholder.svg'}
                                    alt={f.title}
                                    width={64}
                                    height={64}
                                    className="object-cover transition-transform duration-500 group-hover:scale-105"
                                />
                            </div>
                            <h3 className="text-lg text-dark">
                                {f.title}
                            </h3>
                        </div>
                    ))}
                </div>

                <div className="order-1 md:order-2 mb-0 md:mb-8">
                    <h2 className="text-3xl md:text-4xl font-medium text-gray-900 leading-tight">
                        {overview.heading}
                    </h2>
                    <div className="w-12 h-1 bg-blue-900 mb-3" />
                    <p className="mt-4 text-lg text-gray-700 ">
                        {overview.description}
                    </p>
                    <div className="mt-8">
                        <Link href={overview.linkUrl}>
                            <p className="text-xl font-medium text-brand hover:underline">
                                {overview.linkLabel} <ChevronRight className="w-6 h-6 inline-block" />
                            </p>
                        </Link>
                    </div>
                </div>
            </div>
        </section>

    );
}
