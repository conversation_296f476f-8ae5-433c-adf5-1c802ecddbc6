import { Facebook, Instagram, Mail, MessageCircle, Phone, Twitter } from "lucide-react"
import React from "react"

const TopBar = () => {
    return (
        <div className="px-4 py-2 bg-light text-sm">
            <div className="container mx-auto flex justify-between items-center gap-8">
                <div className="flex flex-row items-center gap-6 text-dark">
                    <div className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        <span className="md:text-sm text-xs"><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-1">
                        <Phone className="h-4 w-4" />
                        <MessageCircle className=" hidden md:flex h-4 w-4" />
                        <span className="md:text-sm text-xs">+977 9856016904</span>
                    </div>
                </div>
                <div className="hidden md:flex items-center gap-4 text-dark">
                    <p>Follow Us:</p>
                    <a href="https://facebook.com" aria-label="Facebook" target="_blank" rel="noreferrer">
                        <Facebook className="h-4 w-4 hover:text-brand transition" />
                    </a>
                    <a href="https://twitter.com" aria-label="Twitter" target="_blank" rel="noreferrer">
                        <Twitter className="h-4 w-4 hover:text-brand transition" />
                    </a>
                    <a href="https://instagram.com" aria-label="Instagram" target="_blank" rel="noreferrer">
                        <Instagram className="h-4 w-4 hover:text-brand transition" />
                    </a>
                </div>
            </div>
        </div>
    )
}

export default TopBar