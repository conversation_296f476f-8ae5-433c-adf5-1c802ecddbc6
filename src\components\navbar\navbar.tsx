'use client';

import { Chevron<PERSON>eft, ChevronRight, Menu } from 'lucide-react';
import Link from 'next/link';
import { useState, useRef, useEffect } from 'react';

import { Button } from '@/components/ui/button';
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from '@/components/ui/navigation-menu';
import {
  Sheet,
  SheetTrigger,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from '@/components/ui/sheet';
import Image from 'next/image';
import TopBar from './topbar';
import { IPackage, IActivity, IRegion } from '@/types/package';
import { useActivities } from '@/modules/activity/queries/use-get-activitise';
import { useRegionsByActivitySlug } from '@/modules/region/queries/use-get-regions';
import { useQuery } from '@tanstack/react-query';

type NavItem = {
  title: string;
  href?: string;
  hasDropdown?: boolean;
  items?: NavItem[];
  type?: 'activity' | 'region' | 'package' | 'static';
  slug?: string;
  parentActivity?: string;
};

// Static navigation items (non-dynamic)
const staticNavigationItems: NavItem[] = [
  // { title: 'PEAK CLIMBING', href: '/peak-climbing', type: 'static' },
  { title: 'FASTPACKING GEARS', href: '/fastpacking-gears', type: 'static' },
  { title: 'BLOGS', href: '/blogs', type: 'static' },
];

// Fetch packages by region slug
const fetchPackagesByRegionSlug = async (
  regionSlug: string
): Promise<IPackage[]> => {
  const response = await fetch(
    `https://api.trailandtreknepal.com/package/region-slug/${regionSlug}?sort=createdAt%3Adesc`,
    { mode: 'cors' }
  );

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();

  if (!data.success) {
    throw new Error(data.message || 'Failed to fetch packages');
  }

  return data.data;
};

const usePackagesByRegionSlug = (regionSlug: string) => {
  return useQuery({
    queryKey: ['packages', regionSlug],
    queryFn: () => fetchPackagesByRegionSlug(regionSlug),
    enabled: !!regionSlug,
    staleTime: 10 * 60 * 1000,
    gcTime: 30 * 60 * 1000,
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Component to handle the initial dropdown content for activities
const ActivityDropdownContent = ({ item }: { item: NavItem }) => {
  const { data: regionsData, isLoading: regionsLoading } =
    useRegionsByActivitySlug(item.type === 'activity' ? item.slug || '' : '');

  if (regionsLoading) {
    return (
      <div className="w-[350px] p-2 max-h-[400px] overflow-y-auto">
        <div className="p-3 text-sm text-gray-500">Loading regions...</div>
      </div>
    );
  }

  if (!regionsData?.data || regionsData.data.length === 0) {
    return (
      <div className="w-[350px] p-2 max-h-[400px] overflow-y-auto">
        <div className="p-3 text-sm text-gray-500">No regions found</div>
      </div>
    );
  }

  const regions = regionsData.data.map((region: IRegion) => ({
    title: region.name,
    href: `/${item.slug}/${region.slug}`,
    type: 'region' as const,
    slug: region.slug,
    hasDropdown: true,
    parentActivity: item.slug,
  }));

  return (
    <div className="w-[350px] p-2 max-h-[400px] overflow-y-auto relative">
      <div className="grid gap-1">
        {regions.map((region, index) => (
          <NestedDropdownItem key={`${region.slug || index}`} item={region} />
        ))}
      </div>
    </div>
  );
};

// Component for nested dropdown items with dynamic loading
const NestedDropdownItem = ({ item }: { item: NavItem }) => {
  const [showNested, setShowNested] = useState(false);
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const itemRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Fetch regions when hovering over an activity
  const { data: regionsData, isLoading: regionsLoading } =
    useRegionsByActivitySlug(
      item.type === 'activity' && showNested ? item.slug || '' : ''
    );

  // Fetch packages when hovering over a region
  const { data: packagesData, isLoading: packagesLoading } =
    usePackagesByRegionSlug(
      item.type === 'region' && showNested ? item.slug || '' : ''
    );

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    if (itemRef.current) {
      const rect = itemRef.current.getBoundingClientRect();
      const mainDropdown = itemRef.current.closest(
        '[data-radix-navigation-menu-content]'
      );
      const mainRect = mainDropdown?.getBoundingClientRect();

      setPosition({
        x: (mainRect?.right || rect.right) + 8,
        y: rect.top,
      });
    }
    setShowNested(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setShowNested(false);
    }, 150);
  };

  const handleNestedMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setShowNested(true);
  };

  const handleNestedMouseLeave = () => {
    setShowNested(false);
  };

  const cleanup = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  };

  // Get nested items based on type and API data
  const getNestedItems = (): NavItem[] => {
    if (item.type === 'activity' && regionsData?.data) {
      return regionsData.data.map((region: IRegion) => ({
        title: region.name,
        href: `/${item.slug}/${region.slug}`,
        type: 'region' as const,
        slug: region.slug,
        hasDropdown: true,
        parentActivity: item.slug,
      }));
    }

    if (item.type === 'region' && packagesData) {
      return packagesData
        .filter((pkg: IPackage) => pkg.activity.slug === item.parentActivity)
        .map((pkg: IPackage) => ({
          title: pkg.name,
          href: `/${pkg.activity.slug}/${pkg.slug}`,
          type: 'package' as const,
          slug: pkg.slug,
        }));
    }


    return item.items || [];
  };

  const nestedItems = getNestedItems();
  const hasNestedItems = nestedItems.length > 0;
  const isLoading =
    (item.type === 'activity' && regionsLoading) ||
    (item.type === 'region' && packagesLoading);

  if (item.hasDropdown || hasNestedItems) {
    return (
      <>
        <div
          ref={itemRef}
          className="relative"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <div className="flex items-center justify-between p-3 rounded-md hover:bg-accent hover:text-accent-foreground cursor-pointer">
            <span className="text-sm font-medium">{item.title}</span>
            <ChevronRight className="h-4 w-4" />
          </div>
        </div>

        {showNested && (
          <div
            className="fixed w-[300px] bg-white border rounded-md shadow-xl z-[9999] transition-all duration-200"
            style={{
              left: `${position.x}px`,
              top: `${position.y}px`,
            }}
            onMouseEnter={handleNestedMouseEnter}
            onMouseLeave={handleNestedMouseLeave}
          >
            <div className="p-2 max-h-[400px] overflow-y-auto">
              {isLoading ? (
                <div className="p-3 text-sm text-gray-500">Loading...</div>
              ) : nestedItems.length > 0 ? (
                nestedItems.map((nestedItem, index) => (
                  <NestedDropdownItem
                    key={`${nestedItem.slug || index}`}
                    item={nestedItem}
                  />
                ))
              ) : (
                <div className="p-3 text-sm text-gray-500">No items found</div>
              )}
            </div>
          </div>
        )}
      </>
    );
  }

  return (
    <NavigationMenuLink asChild>
      <Link
        href={item.href ?? '#'}
        className="block select-none rounded-md p-3 leading-none no-underline transition-colors hover:bg-accent hover:text-accent-foreground"
        onClick={() => cleanup()}
      >
        <div className="text-sm font-medium">{item.title}</div>
      </Link>
    </NavigationMenuLink>
  );
};

export default function Component() {
  const [isOpen, setIsOpen] = useState(false);
  const [navigationStack, setNavigationStack] = useState<NavItem[]>([]);
  const [currentRegionSlug, setCurrentRegionSlug] = useState<string>('');

  // Fetch activities for dynamic navigation
  const { data: activitiesData, isLoading: activitiesLoading } =
    useActivities();

  // Fetch regions for mobile navigation
  const { data: regionsData } = useRegionsByActivitySlug(currentRegionSlug);

  // Fetch packages for mobile navigation
  const { data: packagesData } = usePackagesByRegionSlug(currentRegionSlug);

  // Build navigation items combining dynamic activities and static items
  const navigationItems: NavItem[] = [
    ...(
      activitiesData?.data?.map((activity: IActivity) => ({
        title: activity.name.toUpperCase(),
        href: `/${activity.slug}`,
        hasDropdown: true,
        type: 'activity' as const,
        slug: activity.slug,
        showOnNav: activity.showOnNav,
      })) || []
    ).filter((item) => item.showOnNav),
    ...staticNavigationItems,
  ];

  const currentMenu =
    navigationStack.length === 0 ? navigationItems : getCurrentMenuItems();

  function getCurrentMenuItems(): NavItem[] {
    const current = navigationStack[navigationStack.length - 1];

    if (current.type === 'activity' && regionsData?.data) {
      if (regionsData.data.length === 0) {
        return [{
          title: 'No regions found',
          type: 'static',
          href: '#'
        }];
      }
      return regionsData.data.map((region: IRegion) => ({
        title: region.name,
        href: `/${current.slug}/${region.slug}`,
        type: 'region' as const,
        slug: region.slug,
        hasDropdown: true,
      }));
    }

    if (current.type === 'region' && packagesData) {
      if (packagesData.length === 0) {
        return [{
          title: 'No packages found',
          type: 'static',
          href: '#'
        }];
      }
      return packagesData
        .filter((pkg: IPackage) => pkg.activity.slug === navigationStack[0]?.slug) // match parent activity
        .map((pkg: IPackage) => ({
          title: pkg.name,
          href: `/${pkg.activity.slug}/${pkg.slug}`,
          type: 'package' as const,
          slug: pkg.slug,
        }));
    }

    return current.items || [];
  }

  const handleOpenSection = (item: NavItem) => {
    if (
      item.hasDropdown ||
      item.type === 'activity' ||
      item.type === 'region'
    ) {
      setNavigationStack([...navigationStack, item]);

      // Set current region slug for fetching data
      if (item.type === 'activity') {
        setCurrentRegionSlug(item.slug || '');
      } else if (item.type === 'region') {
        setCurrentRegionSlug(item.slug || '');
      }
    } else {
      setIsOpen(false);
    }
  };

  const handleBack = () => {
    const newStack = navigationStack.slice(0, -1);
    setNavigationStack(newStack);

    if (newStack.length === 0) {
      setCurrentRegionSlug('');
    } else {
      const currentItem = newStack[newStack.length - 1];
      if (currentItem.type === 'activity') {
        setCurrentRegionSlug(currentItem.slug || '');
      } else if (currentItem.type === 'region') {
        setCurrentRegionSlug(currentItem.slug || '');
      } else {
        setCurrentRegionSlug('');
      }
    }
  };

  const handleSheetOpenChange = (open: boolean) => {
    if (!open) {
      setNavigationStack([]);
      setCurrentRegionSlug('');
    }
    setIsOpen(open);
  };

  if (activitiesLoading) {
    return (
      <header className="w-full sticky top-0 z-30">
        <TopBar />
        <nav className="bg-light border-b border-gray-200 px-4 py-4">
          <div className="container mx-auto flex items-center justify-between">
            <div className="flex items-center relative h-15 w-30">
              <Link href="/" className="flex items-center gap-3">
                <Image
                  src="/images/logo/fastpacking-logo.png"
                  alt="North Nepal Logo"
                  fill
                />
              </Link>
            </div>
            <div className="text-sm text-gray-500">Loading navigation...</div>
          </div>
        </nav>
      </header>
    );
  }

  return (
    <header className="w-full sticky top-0 z-30">
      <TopBar />
      <nav className="bg-light border-b border-gray-200 px-4 py-4">
        <div className="container mx-auto flex items-center justify-between">
          {/* Logo */}
          <div className="flex items-center relative h-15 w-30">
            <Link href="/" className="flex items-center gap-3">
              <Image
                src="/images/logo/fastpacking-logo.png"
                alt="North Nepal Logo"
                fill
              />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center gap-2">
            <NavigationMenu>
              <NavigationMenuList className="gap-1">
                {navigationItems.map((item) => (
                  <NavigationMenuItem key={item.title + item.slug}>
                    {item.hasDropdown ? (
                      <>
                        <NavigationMenuTrigger className="text-dark hover:text-dark/80 font-medium text-sm bg-transparent hover:bg-transparent data-[state=open]:text-dark/80">
                          <Link
                            href={item.href ?? '#'}
                            className="text-dark hover:text-dark/80 font-medium text-sm"
                          >
                            {item.title}
                          </Link>
                        </NavigationMenuTrigger>
                        <NavigationMenuContent>
                          <ActivityDropdownContent item={item} />
                        </NavigationMenuContent>
                      </>
                    ) : (
                      <NavigationMenuLink asChild>
                        <Link
                          href={item.href ?? '#'}
                          className="text-dark hover:text-dark/80 font-medium text-sm"
                        >
                          {item.title}
                        </Link>
                      </NavigationMenuLink>
                    )}
                  </NavigationMenuItem>
                ))}
              </NavigationMenuList>
            </NavigationMenu>
          </div>

          {/* Desktop CTA */}
          <div className="hidden lg:block">
            <Link href="/customize-my-trip">
              <Button className="bg-brand hover:bg-brand/80 text-white px-6 py-2 rounded-md font-medium">
                Book Now
              </Button>
            </Link>
          </div>

          {/* Mobile Navigation */}
          <Sheet open={isOpen} onOpenChange={handleSheetOpenChange}>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon" className="lg:hidden">
                <Menu className="h-6 w-6" />
                <span className="sr-only">Toggle menu</span>
              </Button>
            </SheetTrigger>

            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <SheetHeader>
                <SheetTitle>
                  {navigationStack.length === 0 ? (
                    'Menu'
                  ) : (
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={handleBack}
                        aria-label="Back"
                      >
                        <ChevronLeft className="h-5 w-5" />
                      </Button>
                      <span className="font-medium text-lg">
                        {navigationStack[navigationStack.length - 1].title}
                      </span>
                    </div>
                  )}
                </SheetTitle>
              </SheetHeader>

              <div className="flex flex-col gap-6 p-2">
                {currentMenu.map((item) =>
                  item.hasDropdown ||
                    item.type === 'activity' ||
                    item.type === 'region' ? (
                    <button
                      key={item.title}
                      type="button"
                      onClick={() => handleOpenSection(item)}
                      className="w-full flex justify-between items-center font-medium text-sm py-2 text-gray-700 hover:text-teal-600"
                    >
                      {item.title}
                      <ChevronRight className="h-5 w-5" />
                    </button>
                  ) : (
                    <Link
                      key={item.title}
                      href={item.href ?? '#'}
                      onClick={() => setIsOpen(false)}
                      className="text-gray-700 hover:text-teal-600 font-medium text-sm py-2 block"
                    >
                      {item.title}
                    </Link>
                  )
                )}
                <Button
                  className="bg-brand hover:bg-brand/80 text-light w-full mt-4"
                  onClick={() => setIsOpen(false)}
                >
                  <Link href="/customize-my-trip">Book Now</Link>
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </nav>
    </header>
  );
}
