'use client';

import React from 'react';
import { Calendar, Clock, Tag } from 'lucide-react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import BlogCard from '@/components/common/cards/blog/blog-card';
import HtmlContentDisplay from '@/components/html-content-display';
import { IBlog } from '@/types/blogs';

interface BlogDetailProps {
  blog: IBlog;
}

const BlogDetail: React.FC<BlogDetailProps> = ({ blog }) => {
  // const related = blogPosts
  //   .filter((p) => p.category === blog.category && p.slug !== blog.slug)
  //   .slice(0, 3)
  const blogPosts: IBlog[] = [];

  const related = blogPosts.filter((p) => p.slug !== blog.slug).slice(0, 3);

  const router = useRouter();

  if (!blog) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Blog not found
          </h1>
          <button
            onClick={() => router.push('/blogs')}
            className="text-brand hover:underline"
          >
            Back to blogs
          </button>
        </div>
      </div>
    );
  }

  return (
    <article className="min-h-screen bg-white">
      <div className="relative h-96 overflow-hidden">
        <Image
          src={blog.image}
          alt={blog.title}
          fill
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-black/40" />

        <div className="absolute bottom-0 left-0 right-0 p-8 bg-gradient-to-t from-black/60 to-transparent">
          <div className="container mx-auto">
            {/* <div className="flex items-center gap-2 mb-3">
              <Tag size={16} className="text-white/80" />
              <span className="text-white/80 text-sm">{blog.category}</span>
            </div> */}
            <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
              {blog.title}
            </h1>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="container mx-auto">
          <div className="flex flex-wrap items-center gap-6 text-sm text-gray-600 mb-8 pb-8 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Calendar size={16} />
              <span>
                {new Date(blog.publishedDate).toLocaleDateString('en-US', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric',
                })}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Clock size={16} />
              <span>{blog.readTime}</span>
            </div>
          </div>

          <div className="text-xl text-gray-700 leading-relaxed mb-8 p-6 bg-gray-50 rounded-lg border-l-4 border-brand">
            {blog.excerpt}
          </div>

          {/* Main Content */}
          <HtmlContentDisplay htmlContent={blog.content} className="prose" />
        </div>

        {related.length > 0 && (
          <section className="mt-16">
            <h2 className="text-2xl font-bold text-dark mb-6">Related Posts</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {related.map((post) => (
                <BlogCard key={post.id} blog={post} />
              ))}
            </div>
          </section>
        )}
      </div>
    </article>
  );
};

export default BlogDetail;
